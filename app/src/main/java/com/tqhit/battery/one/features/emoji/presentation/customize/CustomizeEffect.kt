package com.tqhit.battery.one.features.emoji.presentation.customize

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig

/**
 * Sealed class representing one-time side effects for the Customize screen.
 * These effects are separate from UI state to prevent re-triggering issues when state is recreated.
 * 
 * Effects are consumed once by the UI and should not be stored in persistent state.
 * This follows modern MVI pattern where side effects are handled separately from UI state.
 */
sealed class CustomizeEffect {
    
    /**
     * Navigation effects for moving between screens
     */
    sealed class Navigation : CustomizeEffect() {
        /**
         * Navigate back to the previous screen
         */
        object NavigateBack : Navigation()
        
        /**
         * Navigate to the gallery screen
         */
        object NavigateToGallery : Navigation()
        
        /**
         * Navigate to settings screen
         */
        object NavigateToSettings : Navigation()
    }
    
    /**
     * User feedback effects for showing messages and notifications
     */
    sealed class UserFeedback : CustomizeEffect() {
        /**
         * Show success message when customization is applied successfully
         */
        data class ShowSuccessMessage(val message: String) : UserFeedback()
        
        /**
         * Show error message when an operation fails
         */
        data class ShowErrorMessage(val message: String) : UserFeedback()
        
        /**
         * Show a toast message to the user
         */
        data class ShowToast(val message: String) : UserFeedback()
        
        /**
         * Show a snackbar message with optional action
         */
        data class ShowSnackbar(
            val message: String,
            val actionText: String? = null,
            val action: (() -> Unit)? = null
        ) : UserFeedback()
        
        /**
         * Show validation error message
         */
        data class ShowValidationError(val message: String) : UserFeedback()
    }
    
    /**
     * System interaction effects for permissions and external actions
     */
    sealed class SystemInteraction : CustomizeEffect() {
        /**
         * Request accessibility permissions from the system
         */
        object RequestAccessibilityPermissions : SystemInteraction()
        
        /**
         * Open system settings for accessibility permissions
         */
        object OpenAccessibilitySettings : SystemInteraction()
        
        /**
         * Request overlay permissions from the system
         */
        object RequestOverlayPermissions : SystemInteraction()
        
        /**
         * Open system settings for overlay permissions
         */
        object OpenOverlaySettings : SystemInteraction()
        
        /**
         * Start the accessibility service
         */
        object StartAccessibilityService : SystemInteraction()
        
        /**
         * Stop the accessibility service
         */
        object StopAccessibilityService : SystemInteraction()
        
        /**
         * Trigger a reward ad for premium content unlock
         */
        data class ShowRewardAd(val customizationConfig: CustomizationConfig) : SystemInteraction()
        
        /**
         * Open the premium purchase flow
         */
        object OpenPremiumPurchase : SystemInteraction()
        
        /**
         * Trigger haptic feedback
         */
        object TriggerHapticFeedback : SystemInteraction()
    }
    
    /**
     * Data effects for triggering data operations
     */
    sealed class DataOperation : CustomizeEffect() {
        /**
         * Save customization configuration
         */
        data class SaveCustomization(val config: CustomizationConfig) : DataOperation()
        
        /**
         * Load customization configuration
         */
        object LoadCustomization : DataOperation()
        
        /**
         * Reset customization to defaults
         */
        object ResetToDefaults : DataOperation()
        
        /**
         * Refresh preview data
         */
        object RefreshPreview : DataOperation()
        
        /**
         * Apply customization and enable overlay
         */
        data class ApplyAndEnableOverlay(val config: CustomizationConfig) : DataOperation()
    }
    
    /**
     * Analytics effects for tracking user interactions
     */
    sealed class Analytics : CustomizeEffect() {
        /**
         * Track customization applied event
         */
        data class TrackCustomizationApplied(
            val batteryStyleName: String,
            val emojiStyleName: String,
            val isPremium: Boolean
        ) : Analytics()
        
        /**
         * Track style selection event
         */
        data class TrackStyleSelected(
            val styleName: String,
            val styleType: String, // "battery" or "emoji"
            val isPremium: Boolean
        ) : Analytics()
        
        /**
         * Track customization option changed
         */
        data class TrackCustomizationChanged(
            val optionName: String,
            val optionValue: String
        ) : Analytics()
        
        /**
         * Track premium unlock attempt
         */
        data class TrackPremiumUnlockAttempt(
            val styleName: String,
            val method: String // "reward_ad" or "purchase"
        ) : Analytics()
        
        /**
         * Track error occurrence
         */
        data class TrackError(val errorType: String, val errorMessage: String) : Analytics()
        
        /**
         * Track screen view
         */
        data class TrackScreenView(val screenName: String) : Analytics()
        
        /**
         * Track user engagement
         */
        data class TrackEngagement(
            val action: String,
            val timeSpentMs: Long
        ) : Analytics()
    }
    
    /**
     * UI interaction effects for complex UI operations
     */
    sealed class UIInteraction : CustomizeEffect() {
        /**
         * Show color picker dialog
         */
        object ShowColorPicker : UIInteraction()
        
        /**
         * Hide color picker dialog
         */
        object HideColorPicker : UIInteraction()
        
        /**
         * Update preview with new configuration
         */
        data class UpdatePreview(val config: CustomizationConfig) : UIInteraction()
        
        /**
         * Animate apply button success
         */
        object AnimateApplySuccess : UIInteraction()
        
        /**
         * Animate apply button error
         */
        object AnimateApplyError : UIInteraction()
        
        /**
         * Focus on specific input field
         */
        data class FocusInput(val inputId: String) : UIInteraction()
        
        /**
         * Scroll to specific section
         */
        data class ScrollToSection(val sectionId: String) : UIInteraction()
    }
    
    /**
     * Permission flow effects for handling complex permission scenarios
     */
    sealed class PermissionFlow : CustomizeEffect() {
        /**
         * Show permission explanation dialog
         */
        data class ShowPermissionExplanation(
            val permissionType: String,
            val explanation: String
        ) : PermissionFlow()
        
        /**
         * Show permission denied dialog with retry option
         */
        data class ShowPermissionDenied(
            val permissionType: String,
            val canRetry: Boolean
        ) : PermissionFlow()
        
        /**
         * Show permission granted confirmation
         */
        data class ShowPermissionGranted(val permissionType: String) : PermissionFlow()
        
        /**
         * Handle permission result and proceed with next step
         */
        data class HandlePermissionResult(
            val permissionType: String,
            val granted: Boolean,
            val nextAction: CustomizeEffect?
        ) : PermissionFlow()
    }
}
