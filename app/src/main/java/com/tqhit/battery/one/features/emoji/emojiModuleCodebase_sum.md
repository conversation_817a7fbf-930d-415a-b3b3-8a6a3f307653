# Summary of the Emoji Battery Module Codebase

## 1. Overview

This codebase implements a user-facing "Emoji Battery" feature. The goal is to provide a highly customizable battery indicator in the Android status bar, replacing the system default with a dynamic, emoji-based overlay. The module is well-documented with a Product Requirements Document (`emoji-prd.md`) and a completion report for Phase 0, indicating a structured development process.

The feature consists of three main components:
1.  **Gallery Screen (`EmojiBatteryFragment`):** A browsable grid of available battery styles, filterable by categories.
2.  **Customization Screen (`EmojiCustomizeActivity`):** A screen for live-previewing and modifying a selected battery style's appearance (e.g., size, colors, visibility of elements). This has been updated to support mix-and-match customization of battery and emoji components.
3.  **Overlay Service (`EmojiBatteryAccessibilityService`):** An accessibility service that draws the custom emoji battery icon over the system's status bar.

## 2. Core Architecture

The module is architected using modern, standard Android development patterns:

*   **Clean Architecture:** The code is well-organized into three distinct layers:
    *   `presentation`: Contains Activities, Fragments, ViewModels, UI State/Events, and Adapters.
    *   `domain`: Contains core business logic, including Models, Repository Interfaces, and Use Cases.
    *   `data`: Contains Repository Implementations and remote/local data sources.
*   **Model-View-Intent (MVI):** The presentation layer uses an MVI pattern. UI state is represented by immutable `State` data classes (`BatteryGalleryState`, `CustomizeState`), and all user interactions and system events are handled through a sealed `Event` class hierarchy. State updates are managed within the ViewModel and observed by the UI using Kotlin `StateFlow`.
*   **Dependency Injection (DI):** Hilt is used for dependency injection throughout the module, simplifying the management of dependencies like repositories, use cases, and services.

## 3. Key Components by Layer

### Presentation Layer
*   **`EmojiBatteryFragment`:** The main entry point. Displays a grid of `BatteryStyle` items and a list of `EmojiCategory` tabs for filtering. It handles user interactions like category selection and navigation to the `EmojiCustomizeActivity`. It now uses `EmojiCategoryService` to populate the category tabs from Firebase Remote Config.
*   **`EmojiCustomizeActivity`:** Allows users to modify a selected `BatteryStyle`. It features a live preview that updates in real-time and provides controls for various style properties. It now supports mix-and-match customization of battery and emoji components.
*   **`BatteryGalleryViewModel`:** Manages the state for the gallery screen. It fetches categories and items from the data layer, applies filters, and exposes the final UI state.
*   **Adapters (`BatteryStyleAdapter`, `CategoryAdapter`):** Standard `RecyclerView` adapters for displaying the grid of styles and the horizontal list of categories.

### Domain Layer
*   **Models:**
    *   **`BatteryStyle`:** The primary domain model representing a complete, displayable style. It is used by the UI. It now includes more image URLs for different purposes (gallery, preview, overlay).
    *   **`EmojiCategory` & `EmojiItem`:** Data models that directly map to the JSON structure provided by Firebase Remote Config. There is a crucial conversion function, `EmojiItem.toBatteryStyle()`, that bridges the remote data structure to the UI's domain model.
    *   **`CustomizationConfig`:** A model for persisting all user-selected customization settings. It has been updated to support direct image URLs, bypassing the need for a `selectedStyleId`.
*   **Repositories (Interfaces):** `CustomizationRepository` defines the contract for data access.
*   **Use Cases:** `LoadCustomizationUseCase` and `SaveCustomizationUseCase` encapsulate specific business logic.

### Data Layer
*   **Repository Implementations:**
    *   **`CustomizationRepositoryImpl`:** Implements persistence for user settings using Jetpack DataStore.
*   **Services:**
    *   **`EmojiCategoryService` & `EmojiItemService`:** These services are responsible for fetching data from Firebase Remote Config. `EmojiCategoryService` fetches a list of categories from the `emoji_categories` key, and `EmojiItemService` fetches items for each category from separate keys (e.g., `hot_category`, `animal_category`).

## 4. Data Flow & Content Management

The module uses a granular data-loading strategy from Firebase Remote Config (RC):

1.  The `BatteryGalleryViewModel` uses `EmojiCategoryService` to get the list of category tabs.
2.  When a user selects a category, it uses `EmojiItemService` to fetch the corresponding items for that category ID.
3.  These `EmojiItem` objects are then converted into `BatteryStyle` objects for the UI adapter.

The fallback mechanism is unified and relies on the standard Firebase Remote Config SDK functionality. The app uses `remote_config_defaults.xml` to provide default values for categories and items. If the network fetch fails, the SDK automatically returns the values from this XML file, ensuring a consistent and reliable user experience.