package com.tqhit.battery.one.features.emoji.presentation.gallery

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.domain.model.toCategoryId
import com.tqhit.battery.one.features.emoji.domain.model.EmojiCategory
import com.tqhit.battery.one.features.emoji.domain.model.EmojiItem
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import com.tqhit.battery.one.features.emoji.data.service.EmojiCategoryService
import com.tqhit.battery.one.features.emoji.data.service.EmojiItemService
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Refactored ViewModel for the Battery Gallery screen following modern MVI pattern.
 * Acts as a lightweight orchestrator that coordinates between StateManager and EffectHandler.
 *
 * This refactored ViewModel follows the StatsChargeViewModel pattern:
 * - Uses StateManager for all state transformations
 * - Uses EffectHandler for side effects
 * - Separates state from effects using SharedFlow for one-time events
 * - Reduces responsibilities and improves testability
 * - Maintains backward compatibility with existing Fragment
 */
@HiltViewModel
class BatteryGalleryViewModel @Inject constructor(
    private val emojiCategoryService: EmojiCategoryService,
    private val emojiItemService: EmojiItemService,
    private val customizationRepository: CustomizationRepository,
    private val appRepository: AppRepository,
    private val stateManager: BatteryGalleryStateManager,
    private val effectHandler: BatteryGalleryEffectHandler
) : ViewModel() {

    companion object {
        private const val TAG = "BatteryGalleryVM"
    }

    // Private mutable UI state
    private val _uiState = MutableStateFlow(BatteryGalleryUiState.initial())
    private val _emojiCategories = MutableStateFlow<List<EmojiCategory>>(emptyList())
    private val _currentEmojiItems = MutableStateFlow<List<EmojiItem>>(emptyList())

    // Public read-only state
    val uiState: StateFlow<BatteryGalleryUiState> = _uiState.asStateFlow()
    val emojiCategories: StateFlow<List<EmojiCategory>> = _emojiCategories.asStateFlow()
    val currentEmojiItems: StateFlow<List<EmojiItem>> = _currentEmojiItems.asStateFlow()

    // Public effects flow
    val effects: SharedFlow<BatteryGalleryEffect> = effectHandler.effects

    init {
        Log.d(TAG, "REFACTORED_VM: BatteryGalleryViewModel initialized with StateManager and EffectHandler")
        loadEmojiCategories()
        observeGlobalToggleState()

        // Debug: Log available categories
        logAvailableCategories()
    }

    /**
     * Handles events from the UI - refactored to use StateManager and EffectHandler
     */
    fun handleEvent(event: BatteryGalleryEvent) {
        Log.d(TAG, "REFACTORED_VM: Handling event: ${event::class.simpleName}")

        when (event) {
            is BatteryGalleryEvent.LoadInitialData -> {
                Log.d(TAG, "REFACTORED_VM: LoadInitialData event - using emoji services")
                // Initial data loading is handled by emoji services in init
            }
            is BatteryGalleryEvent.RefreshData -> {
                Log.d(TAG, "REFACTORED_VM: RefreshData event")
                effectHandler.handleDataRefresh(viewModelScope)
            }
            is BatteryGalleryEvent.RetryLoad -> {
                Log.d(TAG, "REFACTORED_VM: RetryLoad event")
                effectHandler.handleDataRefresh(viewModelScope)
            }
            is BatteryGalleryEvent.SelectCategory -> {
                Log.d(TAG, "REFACTORED_VM: SelectCategory event: ${event.category.displayName}")
                selectCategory(event.category)
            }
            is BatteryGalleryEvent.SearchQueryChanged -> {
                Log.d(TAG, "REFACTORED_VM: SearchQueryChanged event: ${event.query}")
                updateSearchQuery(event.query)
            }
            is BatteryGalleryEvent.ToggleSearchMode -> {
                Log.d(TAG, "REFACTORED_VM: ToggleSearchMode event")
                toggleSearchMode()
            }
            is BatteryGalleryEvent.ClearSearch -> {
                Log.d(TAG, "REFACTORED_VM: ClearSearch event")
                clearSearch()
            }
            is BatteryGalleryEvent.SelectBatteryStyle -> {
                Log.d(TAG, "REFACTORED_VM: SelectBatteryStyle event: ${event.style.name}")
                selectBatteryStyle(event.style)
            }
            is BatteryGalleryEvent.ToggleGlobalFeature -> {
                Log.d(TAG, "REFACTORED_VM: ToggleGlobalFeature event: ${event.isEnabled}")
                toggleGlobalFeature(event.isEnabled)
            }
            is BatteryGalleryEvent.TogglePremiumFilter -> {
                Log.d(TAG, "REFACTORED_VM: TogglePremiumFilter event")
                togglePremiumFilter()
            }
            is BatteryGalleryEvent.ToggleFreeFilter -> {
                Log.d(TAG, "REFACTORED_VM: ToggleFreeFilter event")
                toggleFreeFilter()
            }
            is BatteryGalleryEvent.ClearAllFilters -> {
                Log.d(TAG, "REFACTORED_VM: ClearAllFilters event")
                clearAllFilters()
            }
            is BatteryGalleryEvent.UnlockPremiumStyle -> {
                Log.d(TAG, "REFACTORED_VM: UnlockPremiumStyle event: ${event.style.name}")
                unlockPremiumStyle(event.style)
            }
            is BatteryGalleryEvent.WatchAdToUnlock -> {
                Log.d(TAG, "REFACTORED_VM: WatchAdToUnlock event: ${event.style.name}")
                watchAdToUnlock(event.style)
            }
            is BatteryGalleryEvent.PurchasePremiumUnlock -> {
                Log.d(TAG, "REFACTORED_VM: PurchasePremiumUnlock event: ${event.style.name}")
                purchasePremiumUnlock(event.style)
            }
            is BatteryGalleryEvent.NavigationEvent -> {
                Log.d(TAG, "REFACTORED_VM: NavigationEvent - handled by EffectHandler")
                handleNavigationEvent(event)
            }
            is BatteryGalleryEvent.ErrorEvent -> {
                Log.d(TAG, "REFACTORED_VM: ErrorEvent - handled by EffectHandler")
                handleErrorEvent(event)
            }
            is BatteryGalleryEvent.UIEvent -> {
                Log.d(TAG, "REFACTORED_VM: UIEvent - handled by EffectHandler")
                handleUIEvent(event)
            }
            is BatteryGalleryEvent.AdEvent -> handleAdEvent(event)
            is BatteryGalleryEvent.SystemEvent -> handleSystemEvent(event)
        }
    }

    // Private event handling methods - refactored to use StateManager and EffectHandler

    /**
     * Selects a category for filtering and loads corresponding emoji items
     */
    private fun selectCategory(category: BatteryStyleCategory) {
        Log.d(TAG, "REFACTORED_VM: Selecting category: ${category.displayName}")
        Log.i(TAG, "EMOJI_LOADING_FLOW: ========== CATEGORY SELECTION ==========")
        Log.i(TAG, "EMOJI_LOADING_FLOW: User selected category: ${category.displayName}")

        // Use StateManager for state transformation
        val currentState = _uiState.value
        val newState = stateManager.selectCategory(currentState, category)
        _uiState.value = newState
        Log.i(TAG, "EMOJI_LOADING_FLOW: State updated - displayed styles: ${newState.displayedStyles.size}")

        // Map BatteryStyleCategory to category ID for remote config
        val categoryId = category.toCategoryId()
        Log.d(TAG, "REFACTORED_VM: Mapped category ${category.displayName} to categoryId: $categoryId")
        Log.i(TAG, "EMOJI_LOADING_FLOW: Mapped to remote config key: $categoryId")

        // Load emoji items for this category
        loadEmojiItemsForCategory(categoryId)

        // Handle analytics through EffectHandler
        effectHandler.handleCategorySelection(category.displayName, viewModelScope)
    }

    /**
     * Updates the search query and filters styles - refactored to use StateManager
     */
    private fun updateSearchQuery(query: String) {
        Log.d(TAG, "REFACTORED_VM: Updating search query: $query")

        val currentState = _uiState.value
        val newState = stateManager.updateSearchQuery(currentState, query)
        _uiState.value = newState

        // Handle search analytics through EffectHandler
        if (query.isNotBlank()) {
            effectHandler.handleSearchPerformed(query, newState.displayedStyles.size, viewModelScope)
        }
    }

    /**
     * Toggles search mode - refactored to use StateManager
     */
    private fun toggleSearchMode() {
        Log.d(TAG, "REFACTORED_VM: Toggling search mode")

        val currentState = _uiState.value
        val newState = stateManager.toggleSearchMode(currentState)
        _uiState.value = newState
    }

    /**
     * Clears search and exits search mode - refactored to use StateManager
     */
    private fun clearSearch() {
        Log.d(TAG, "REFACTORED_VM: Clearing search")

        val currentState = _uiState.value
        val newState = stateManager.clearSearch(currentState)
        _uiState.value = newState
    }

    /**
     * Handles battery style selection - refactored to use EffectHandler
     */
    private fun selectBatteryStyle(style: BatteryStyle) {
        Log.d(TAG, "REFACTORED_VM: Selected battery style: ${style.name}, isPremium: ${style.isPremium}")

        // Use EffectHandler for navigation and analytics
        effectHandler.handleStyleSelection(style, viewModelScope)
    }

    /**
     * Toggles the global emoji battery feature - refactored to use EffectHandler
     */
    private fun toggleGlobalFeature(isEnabled: Boolean) {
        Log.d(TAG, "REFACTORED_VM: Toggling global feature: $isEnabled")

        // Use EffectHandler for permission handling and state updates
        effectHandler.handleGlobalFeatureToggle(
            isEnabled = isEnabled,
            coroutineScope = viewModelScope,
            onStateUpdate = { enabled ->
                val currentState = _uiState.value
                val newState = stateManager.updateGlobalToggle(currentState, enabled)
                _uiState.value = newState
            }
        )
    }

    /**
     * Handles permission request result - refactored to use EffectHandler
     */
    fun handlePermissionResult(granted: Boolean) {
        Log.d(TAG, "REFACTORED_VM: Permission result: $granted")

        effectHandler.handlePermissionResult(
            granted = granted,
            coroutineScope = viewModelScope,
            onStateUpdate = { enabled ->
                val currentState = _uiState.value
                val newState = stateManager.updateGlobalToggle(currentState, enabled)
                _uiState.value = newState
            },
            onError = { errorMessage ->
                val currentState = _uiState.value
                val newState = stateManager.updateError(currentState, errorMessage)
                _uiState.value = newState
            }
        )
    }
    
    /**
     * Toggles premium filter - refactored to use StateManager
     */
    private fun togglePremiumFilter() {
        Log.d(TAG, "REFACTORED_VM: Toggling premium filter")

        val currentState = _uiState.value
        val newState = stateManager.togglePremiumFilter(currentState)
        _uiState.value = newState
    }

    /**
     * Toggles free filter - refactored to use StateManager
     */
    private fun toggleFreeFilter() {
        Log.d(TAG, "REFACTORED_VM: Toggling free filter")

        val currentState = _uiState.value
        val newState = stateManager.toggleFreeFilter(currentState)
        _uiState.value = newState
    }

    /**
     * Clears all filters - refactored to use StateManager
     */
    private fun clearAllFilters() {
        Log.d(TAG, "REFACTORED_VM: Clearing all filters")

        val currentState = _uiState.value
        val newState = stateManager.clearAllFilters(currentState)
        _uiState.value = newState
    }

    /**
     * Handles premium style unlock - refactored to use EffectHandler
     */
    private fun unlockPremiumStyle(style: BatteryStyle) {
        Log.d(TAG, "REFACTORED_VM: Unlocking premium style: ${style.name}")
        effectHandler.handlePremiumUnlock(style, viewModelScope)
    }
    
    /**
     * Handles watching ad to unlock premium style - refactored to use EffectHandler
     */
    private fun watchAdToUnlock(style: BatteryStyle) {
        Log.d(TAG, "REFACTORED_VM: Watching ad to unlock style: ${style.name}")
        effectHandler.handleAdUnlock(style, viewModelScope)
    }

    /**
     * Handles purchasing premium unlock - refactored to use EffectHandler
     */
    private fun purchasePremiumUnlock(style: BatteryStyle) {
        Log.d(TAG, "REFACTORED_VM: Purchasing premium unlock for style: ${style.name}")
        // Use EffectHandler to trigger premium purchase flow
        effectHandler.handlePremiumUnlock(style, viewModelScope)
    }
    
    /**
     * Updates state with new styles and loading state - refactored to use StateManager
     */
    private fun updateStateWithStyles(styles: List<BatteryStyle>, isLoading: Boolean) {
        Log.d(TAG, "REFACTORED_VM: updateStateWithStyles called with ${styles.size} styles, isLoading=$isLoading")

        val currentState = _uiState.value

        if (isLoading) {
            val newState = stateManager.updateLoading(currentState, true)
            _uiState.value = newState
        } else {
            val newState = stateManager.updateWithStyles(currentState, styles)
            _uiState.value = newState
        }
    }
    
    /**
     * Updates state specifically for emoji items loaded from remote config - refactored to use StateManager
     */
    private fun updateStateWithEmojiItems(emojiStyles: List<BatteryStyle>) {
        Log.d(TAG, "REFACTORED_VM: updateStateWithEmojiItems called with ${emojiStyles.size} emoji styles")

        val currentState = _uiState.value
        val newState = stateManager.updateWithStyles(currentState, emojiStyles)
        _uiState.value = newState

        Log.d(TAG, "REFACTORED_VM: State updated with ${newState.displayedStyles.size} displayed styles")
    }

    /**
     * Observes global toggle state from customization repository
     */
    private fun observeGlobalToggleState() {
        viewModelScope.launch {
            customizationRepository.getCustomizationConfigFlow()
                .catch { exception ->
                    Log.e(TAG, "REFACTORED_VM: Error observing global toggle state", exception)
                }
                .collect { config ->
                    Log.d(TAG, "REFACTORED_VM: Global toggle state updated: ${config.isGlobalEnabled}")
                    val currentState = _uiState.value
                    val newState = stateManager.updateGlobalToggle(currentState, config.isGlobalEnabled)
                    _uiState.value = newState
                }
        }
    }

    /**
     * Handles navigation events - refactored to use EffectHandler
     */
    private fun handleNavigationEvent(event: BatteryGalleryEvent.NavigationEvent) {
        Log.d(TAG, "REFACTORED_VM: Handling navigation event: ${event::class.simpleName}")

        // Use EffectHandler for navigation effects
        viewModelScope.launch {
            when (event) {
                is BatteryGalleryEvent.NavigationEvent.NavigateToCustomize -> {
                    Log.d(TAG, "REFACTORED_VM: Emitting navigate to customize effect for: ${event.style.name}")
                    effectHandler.handleStyleSelection(event.style, viewModelScope)
                }
                is BatteryGalleryEvent.NavigationEvent.ShowPremiumDialog -> {
                    Log.d(TAG, "REFACTORED_VM: Emitting show premium dialog effect")
                    effectHandler.handlePremiumUnlock(event.style, viewModelScope)
                }
                is BatteryGalleryEvent.NavigationEvent.ShowPermissionDialog -> {
                    Log.d(TAG, "REFACTORED_VM: Showing permission dialog")
                    effectHandler.handleShowPermissionDialog(viewModelScope)
                }
                is BatteryGalleryEvent.NavigationEvent.NavigateToSettings -> {
                    Log.d(TAG, "REFACTORED_VM: Settings navigation not implemented yet")
                }
                is BatteryGalleryEvent.NavigationEvent.NavigateBack -> {
                    Log.d(TAG, "REFACTORED_VM: Back navigation handled by system")
                }
            }
        }
    }

    /**
     * Navigation event consumption is no longer needed with effect-based architecture
     */
    @Deprecated("Navigation events are now handled as effects, no consumption needed")
    fun consumeNavigationEvent() {
        Log.d(TAG, "REFACTORED_VM: consumeNavigationEvent called but no longer needed with effects")
    }
    
    /**
     * Handles error events - refactored to use EffectHandler
     */
    private fun handleErrorEvent(event: BatteryGalleryEvent.ErrorEvent) {
        Log.d(TAG, "REFACTORED_VM: Handling error event: ${event::class.simpleName}")

        when (event) {
            is BatteryGalleryEvent.ErrorEvent.ShowError -> {
                val currentState = _uiState.value
                val newState = stateManager.updateError(currentState, event.message)
                _uiState.value = newState

                effectHandler.handleError(
                    errorMessage = event.message,
                    errorType = ErrorType.UNKNOWN_ERROR,
                    isRetryable = true,
                    coroutineScope = viewModelScope
                )
            }
            is BatteryGalleryEvent.ErrorEvent.DismissError -> {
                val currentState = _uiState.value
                val newState = currentState.copy(errorMessage = null, errorType = null)
                _uiState.value = newState
            }
            is BatteryGalleryEvent.ErrorEvent.ShowToast -> {
                effectHandler.handleShowToast(event.message, viewModelScope)
            }
        }
    }

    /**
     * Handles UI events - refactored to use EffectHandler
     */
    private fun handleUIEvent(event: BatteryGalleryEvent.UIEvent) {
        Log.d(TAG, "REFACTORED_VM: Handling UI event: ${event::class.simpleName}")

        viewModelScope.launch {
            when (event) {
                is BatteryGalleryEvent.UIEvent.ShowInfoDialog -> {
                    effectHandler.handleShowInfoDialog(viewModelScope)
                }
                else -> {
                    Log.d(TAG, "REFACTORED_VM: UI event ${event::class.simpleName} handled by fragment")
                }
            }
        }
    }

    /**
     * Handles ad events - refactored to use EffectHandler
     */
    private fun handleAdEvent(event: BatteryGalleryEvent.AdEvent) {
        Log.d(TAG, "REFACTORED_VM: Handling ad event: ${event::class.simpleName}")
        // Ad events are handled through EffectHandler when needed
    }
    
    /**
     * Handles system events
     */
    private fun handleSystemEvent(event: BatteryGalleryEvent.SystemEvent) {
        Log.d(TAG, "Handling system event: ${event::class.simpleName}")
        when (event) {
            is BatteryGalleryEvent.SystemEvent.OnResume -> {
                // Data loading is now handled by the new emoji service system
                // No additional action needed on resume
            }
            else -> {
                // Other system events handled as needed
            }
        }
    }

    /**
     * Loads emoji categories from Firebase Remote Config.
     * Uses EmojiCategoryService to fetch categories with fallback to defaults.
     * Updates the emojiCategories StateFlow for UI observation.
     */
    private fun loadEmojiCategories() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "REMOTE_CONFIG: Loading emoji categories")
                val categories = emojiCategoryService.getEmojiCategories()

                Log.d(TAG, "REMOTE_CONFIG: About to emit ${categories.size} categories to StateFlow")
                _emojiCategories.value = categories
                Log.d(TAG, "REMOTE_CONFIG: Successfully emitted ${categories.size} emoji categories to StateFlow")
                Log.d(TAG, "REMOTE_CONFIG: StateFlow current value has ${_emojiCategories.value.size} categories")

                // Log category details for debugging
                categories.forEachIndexed { index, category ->
                    Log.d(TAG, "REMOTE_CONFIG: Category $index: ${category.name} (id=${category.id}, priority=${category.priority}, isNew=${category.isNew})")
                }
            } catch (e: Exception) {
                Log.e(TAG, "REMOTE_CONFIG: Error loading emoji categories", e)
                handleCategoryLoadError(e)
            }
        }
    }

    /**
     * Loads emoji items for the selected category from Firebase Remote Config.
     * Converts emoji items to BatteryStyle objects for compatibility with existing UI.
     * Updates both currentEmojiItems StateFlow and UI state with converted styles.
     */
    private fun loadEmojiItemsForCategory(categoryId: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "REMOTE_CONFIG: Loading emoji items for category: $categoryId")
                Log.i(TAG, "EMOJI_LOADING_FLOW: ========== LOADING ITEMS ==========")
                Log.i(TAG, "EMOJI_LOADING_FLOW: Starting to load items for category: $categoryId")

                // Set loading state using StateManager
                val currentState = _uiState.value
                val loadingState = stateManager.updateLoading(currentState, true)
                _uiState.value = loadingState
                Log.i(TAG, "EMOJI_LOADING_FLOW: Set loading state to true")

                val items = emojiItemService.getEmojiItemsByCategory(categoryId)
                _currentEmojiItems.value = items
                Log.i(TAG, "EMOJI_LOADING_FLOW: Service returned ${items.size} items for category: $categoryId")

                // Convert to BatteryStyle for compatibility with existing UI
                val batteryStyles = items.map { it.toBatteryStyle() }

                Log.d(TAG, "REMOTE_CONFIG: Loaded ${items.size} emoji items for category: $categoryId")
                Log.d(TAG, "REMOTE_CONFIG: Converted to ${batteryStyles.size} battery styles")
                Log.i(TAG, "EMOJI_LOADING_FLOW: Converted ${items.size} items to ${batteryStyles.size} battery styles")

                // Update UI state directly with emoji items (don't use filtering logic for hardcoded styles)
                updateStateWithEmojiItems(batteryStyles)
                Log.i(TAG, "EMOJI_LOADING_FLOW: Updated UI state with ${batteryStyles.size} styles")

                // Log item details for debugging
                items.forEachIndexed { index, item ->
                    Log.d(TAG, "REMOTE_CONFIG: Item $index in $categoryId: ${item.name} (id=${item.id}, priority=${item.priority}, isPremium=${item.isPremium})")
                }

                // Enhanced logging for empty results
                if (items.isEmpty()) {
                    Log.w(TAG, "EMOJI_LOADING_FLOW: WARNING - No items loaded for category: $categoryId")
                    Log.w(TAG, "EMOJI_LOADING_FLOW: This will result in empty state for this category")
                } else {
                    Log.i(TAG, "EMOJI_LOADING_FLOW: Successfully loaded ${items.size} items for category: $categoryId")
                }

            } catch (e: Exception) {
                Log.e(TAG, "REMOTE_CONFIG: Error loading emoji items for category: $categoryId", e)
                handleItemLoadError(e, categoryId)
            }
        }
    }

    /**
     * Handles errors that occur during category loading with user-friendly messages
     */
    private fun handleCategoryLoadError(exception: Exception) {
        Log.e(TAG, "ERROR_HANDLING: Category load error", exception)

        val errorState = when {
            isNetworkError(exception) -> {
                Log.d(TAG, "ERROR_HANDLING: Network error detected")
                BatteryGalleryUiState.networkError()
            }
            isRemoteConfigError(exception) -> {
                Log.d(TAG, "ERROR_HANDLING: Remote config error detected")
                BatteryGalleryUiState.remoteConfigError()
            }
            isDataParsingError(exception) -> {
                Log.d(TAG, "ERROR_HANDLING: Data parsing error detected")
                BatteryGalleryUiState.dataParsingError()
            }
            else -> {
                Log.d(TAG, "ERROR_HANDLING: Unknown error detected")
                BatteryGalleryUiState.error(
                    message = "Unable to load categories. Please try again.",
                    errorType = ErrorType.UNKNOWN_ERROR,
                    isRecoverable = true
                )
            }
        }

        // Use StateManager to update error state
        val currentState = _uiState.value
        val newState = stateManager.updateError(
            currentState = currentState,
            errorMessage = errorState.errorMessage ?: "Unknown error",
            errorType = errorState.errorType ?: ErrorType.UNKNOWN_ERROR,
            isRecoverable = errorState.isErrorRecoverable
        )
        _uiState.value = newState
    }

    /**
     * Handles errors that occur during item loading with user-friendly messages
     */
    private fun handleItemLoadError(exception: Exception, categoryId: String) {
        Log.e(TAG, "ERROR_HANDLING: Item load error for category: $categoryId", exception)

        val errorMessage = when {
            isNetworkError(exception) -> {
                Log.d(TAG, "ERROR_HANDLING: Network error for items")
                "No internet connection. Please check your network and try again."
            }
            isRemoteConfigError(exception) -> {
                Log.d(TAG, "ERROR_HANDLING: Remote config error for items")
                "Unable to load latest content for this category."
            }
            isDataParsingError(exception) -> {
                Log.d(TAG, "ERROR_HANDLING: Data parsing error for items")
                "Content format error for this category. Please try another category."
            }
            else -> {
                Log.d(TAG, "ERROR_HANDLING: Unknown error for items")
                "Unable to load items for this category. Please try again."
            }
        }

        val errorType = when {
            isNetworkError(exception) -> ErrorType.NETWORK_ERROR
            isRemoteConfigError(exception) -> ErrorType.REMOTE_CONFIG_ERROR
            isDataParsingError(exception) -> ErrorType.DATA_PARSING_ERROR
            else -> ErrorType.UNKNOWN_ERROR
        }

        // Use StateManager to update error state
        val currentState = _uiState.value
        val newState = stateManager.updateError(
            currentState = currentState,
            errorMessage = errorMessage,
            errorType = errorType,
            isRecoverable = true
        )
        _uiState.value = newState
    }

    /**
     * Determines if the exception is a network-related error
     */
    private fun isNetworkError(exception: Exception): Boolean {
        return exception.message?.contains("network", ignoreCase = true) == true ||
                exception.message?.contains("connection", ignoreCase = true) == true ||
                exception.message?.contains("timeout", ignoreCase = true) == true ||
                exception is java.net.UnknownHostException ||
                exception is java.net.SocketTimeoutException ||
                exception is java.io.IOException
    }

    /**
     * Determines if the exception is a remote config related error
     */
    private fun isRemoteConfigError(exception: Exception): Boolean {
        return exception.message?.contains("remote config", ignoreCase = true) == true ||
                exception.message?.contains("firebase", ignoreCase = true) == true
    }

    /**
     * Determines if the exception is a data parsing error
     */
    private fun isDataParsingError(exception: Exception): Boolean {
        return exception.message?.contains("json", ignoreCase = true) == true ||
                exception.message?.contains("parsing", ignoreCase = true) == true ||
                exception is com.google.gson.JsonSyntaxException ||
                exception is com.google.gson.JsonParseException
    }

    /**
     * Clears the current error state
     */
    fun clearError() {
        Log.d(TAG, "ERROR_HANDLING: Clearing error state")
        _uiState.value = _uiState.value.copy(
            errorMessage = null,
            errorType = null,
            isErrorRecoverable = true
        )
    }

    /**
     * Retries the last failed operation based on error type
     */
    fun retryLastOperation() {
        Log.d(TAG, "ERROR_HANDLING: Retrying last operation")
        val currentState = _uiState.value

        when (currentState.errorType) {
            ErrorType.NETWORK_ERROR, ErrorType.REMOTE_CONFIG_ERROR -> {
                Log.d(TAG, "ERROR_HANDLING: Retrying category and item loading")
                clearError()
                loadEmojiCategories()
                // Reload current category items
                val categoryId = currentState.selectedCategory.toCategoryId()
                loadEmojiItemsForCategory(categoryId)
            }
            ErrorType.DATA_PARSING_ERROR -> {
                Log.d(TAG, "ERROR_HANDLING: Retrying item loading only")
                clearError()
                val categoryId = currentState.selectedCategory.toCategoryId()
                loadEmojiItemsForCategory(categoryId)
            }
            else -> {
                Log.d(TAG, "ERROR_HANDLING: Generic retry")
                clearError()
                loadEmojiCategories()
            }
        }
    }

    /**
     * Logs available categories for debugging purposes.
     * Helps identify which categories have data in remote config.
     */
    private fun logAvailableCategories() {
        viewModelScope.launch {
            try {
                Log.i(TAG, "EMOJI_LOADING_FLOW: ========== AVAILABLE CATEGORIES DEBUG ==========")
                val availableCategories = emojiItemService.getAvailableCategoryIdsWithData()
                Log.i(TAG, "EMOJI_LOADING_FLOW: Found ${availableCategories.size} categories with data")

                // Check each category mapping
                BatteryStyleCategory.values().forEach { category ->
                    val categoryId = category.toCategoryId()
                    val hasData = availableCategories.contains(categoryId)
                    val status = if (hasData) "✓ HAS DATA" else "✗ NO DATA"
                    Log.i(TAG, "EMOJI_LOADING_FLOW: ${category.displayName} ($categoryId) - $status")
                }

                Log.i(TAG, "EMOJI_LOADING_FLOW: ========== END CATEGORIES DEBUG ==========")
            } catch (e: Exception) {
                Log.e(TAG, "EMOJI_LOADING_FLOW: Error logging available categories", e)
            }
        }
    }
}
