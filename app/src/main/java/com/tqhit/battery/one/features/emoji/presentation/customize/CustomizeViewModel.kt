package com.tqhit.battery.one.features.emoji.presentation.customize

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase
import com.tqhit.battery.one.features.emoji.data.service.EmojiItemService
import com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityService
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Refactored ViewModel for the emoji battery customization screen following modern MVI pattern.
 * Acts as a lightweight orchestrator that coordinates between StateManager and EffectHandler.
 *
 * This refactored ViewModel follows the StatsChargeViewModel pattern:
 * - Uses StateManager for all state transformations
 * - Uses EffectHandler for side effects
 * - Separates state from effects using SharedFlow for one-time events
 * - Reduces responsibilities and improves testability
 * - Maintains backward compatibility with existing Activity
 */
@HiltViewModel
class CustomizeViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val loadCustomizationUseCase: LoadCustomizationUseCase,
    private val saveCustomizationUseCase: SaveCustomizationUseCase,
    private val emojiItemService: EmojiItemService,
    private val customizationRepository: CustomizationRepository,
    private val stateManager: CustomizeStateManager,
    private val effectHandler: CustomizeEffectHandler
) : ViewModel() {

    companion object {
        private const val TAG = "CustomizeViewModel"
    }

    // Private mutable UI state
    private val _uiState = MutableStateFlow(CustomizeUiState.initial())

    // Public read-only state
    val uiState: StateFlow<CustomizeUiState> = _uiState.asStateFlow()

    // Public effects flow
    val effects: SharedFlow<CustomizeEffect> = effectHandler.effects

    init {
        Log.d(TAG, "REFACTORED_VM: CustomizeViewModel initialized with StateManager and EffectHandler")
        observeDataSources()

        // Track screen view
        effectHandler.handleScreenView("customize_screen", viewModelScope)
    }

    /**
     * Handles events from the UI - refactored to use StateManager and EffectHandler
     */
    fun handleEvent(event: CustomizeEvent) {
        Log.d(TAG, "REFACTORED_VM: Handling event: ${event::class.simpleName}")

        when (event) {
            is CustomizeEvent.LoadInitialData -> {
                Log.d(TAG, "REFACTORED_VM: LoadInitialData event")
                loadInitialData()
            }
            is CustomizeEvent.InitializeWithStyle -> {
                Log.d(TAG, "REFACTORED_VM: InitializeWithStyle event: ${event.style.name}")
                initializeWithStyle(event.style)
            }
            is CustomizeEvent.SelectBatteryStyle -> {
                Log.d(TAG, "REFACTORED_VM: SelectBatteryStyle event: ${event.style.name}")
                selectBatteryStyle(event.style)
            }
            is CustomizeEvent.SelectEmojiStyle -> {
                Log.d(TAG, "REFACTORED_VM: SelectEmojiStyle event: ${event.style.name}")
                selectEmojiStyle(event.style)
            }
            is CustomizeEvent.ToggleGlobalEnabled -> {
                Log.d(TAG, "REFACTORED_VM: ToggleGlobalEnabled event: ${event.enabled}")
                toggleGlobalFeature(event.enabled)
            }
            is CustomizeEvent.HandlePermissionResult -> {
                Log.d(TAG, "REFACTORED_VM: HandlePermissionResult event: ${event.granted}")
                handlePermissionResult(event.granted)
            }
            is CustomizeEvent.ToggleShowEmoji -> {
                Log.d(TAG, "REFACTORED_VM: ToggleShowEmoji event: ${event.show}")
                updateShowEmoji(event.show)
            }
            is CustomizeEvent.ToggleShowPercentage -> {
                Log.d(TAG, "REFACTORED_VM: ToggleShowPercentage event: ${event.show}")
                updateShowPercentage(event.show)
            }
            is CustomizeEvent.UpdatePercentageFontSize -> {
                Log.d(TAG, "REFACTORED_VM: UpdatePercentageFontSize event: ${event.size}")
                updatePercentageFontSize(event.size)
            }
            is CustomizeEvent.UpdateEmojiSizeScale -> {
                Log.d(TAG, "REFACTORED_VM: UpdateEmojiSizeScale event: ${event.scale}")
                updateEmojiSizeScale(event.scale)
            }
            is CustomizeEvent.UpdatePercentageColor -> {
                Log.d(TAG, "REFACTORED_VM: UpdatePercentageColor event: ${event.color}")
                updatePercentageColor(event.color)
            }
            is CustomizeEvent.ShowColorPicker -> {
                Log.d(TAG, "REFACTORED_VM: ShowColorPicker event")
                showColorPicker()
            }
            is CustomizeEvent.HideColorPicker -> {
                Log.d(TAG, "REFACTORED_VM: HideColorPicker event")
                hideColorPicker()
            }
            is CustomizeEvent.SelectColor -> {
                Log.d(TAG, "REFACTORED_VM: SelectColor event: ${event.color}")
                selectColor(event.color, event.index)
            }
            is CustomizeEvent.UpdatePreviewBatteryLevel -> {
                Log.d(TAG, "REFACTORED_VM: UpdatePreviewBatteryLevel event: ${event.level}")
                updatePreviewBatteryLevel(event.level)
            }
            is CustomizeEvent.RefreshPreview -> {
                Log.d(TAG, "REFACTORED_VM: RefreshPreview event")
                refreshPreview()
            }
            is CustomizeEvent.ApplyCustomization -> {
                Log.d(TAG, "REFACTORED_VM: ApplyCustomization event")
                applyCustomization()
            }
            is CustomizeEvent.ApplyPremiumCustomization -> {
                Log.d(TAG, "REFACTORED_VM: ApplyPremiumCustomization event")
                applyPremiumCustomization()
            }
            is CustomizeEvent.ResetToDefaults -> {
                Log.d(TAG, "REFACTORED_VM: ResetToDefaults event")
                resetToDefaults()
            }
            is CustomizeEvent.NavigateBack -> {
                Log.d(TAG, "REFACTORED_VM: NavigateBack event")
                navigateBack()
            }
            is CustomizeEvent.ClearNavigationState -> {
                Log.d(TAG, "REFACTORED_VM: ClearNavigationState event - no longer needed with effects")
            }
            is CustomizeEvent.OnResume -> {
                Log.d(TAG, "REFACTORED_VM: OnResume event")
                onResume()
            }
            is CustomizeEvent.OnPause -> {
                Log.d(TAG, "REFACTORED_VM: OnPause event")
                onPause()
            }
            is CustomizeEvent.RetryLoad -> {
                Log.d(TAG, "REFACTORED_VM: RetryLoad event")
                retryLoad()
            }
            is CustomizeEvent.ClearError -> {
                Log.d(TAG, "REFACTORED_VM: ClearError event")
                clearError()
            }
            is CustomizeEvent.ValidateForm -> {
                Log.d(TAG, "REFACTORED_VM: ValidateForm event")
                validateForm()
            }
            is CustomizeEvent.ShowValidationError -> {
                Log.d(TAG, "REFACTORED_VM: ShowValidationError event: ${event.message}")
                showValidationError(event.message)
            }
        }
    }

    // Private event handling methods - refactored to use StateManager and EffectHandler

    /**
     * Observes data sources and updates UI state reactively - refactored to use StateManager
     */
    private fun observeDataSources() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "REFACTORED_VM: Starting to observe data sources")

                // Load customization config
                loadCustomizationUseCase().collect { config ->
                    Log.d(TAG, "REFACTORED_VM: Customization config updated: ${config.selectedStyleId}")

                    val currentState = _uiState.value
                    val newState = stateManager.updateCustomizationConfig(currentState, config)
                    _uiState.value = newState
                }
            } catch (exception: Exception) {
                Log.e(TAG, "REFACTORED_VM: Error setting up data observation", exception)

                val currentState = _uiState.value
                val newState = stateManager.updateError(currentState, "Failed to initialize: ${exception.message}")
                _uiState.value = newState

                effectHandler.handleError(
                    errorMessage = "Failed to initialize: ${exception.message}",
                    errorType = "data_loading_error",
                    coroutineScope = viewModelScope
                )
            }
        }

        // TODO: Re-implement battery styles loading using new EmojiItemService if needed
        // For now, the customize screen will work with the single style passed to it
        Log.d(TAG, "Battery styles loading removed - using single style customization only")
    }
    
    /**
     * Loads initial data for the customization screen - refactored to use StateManager
     */
    private fun loadInitialData() {
        Log.d(TAG, "REFACTORED_VM: Loading initial data")

        val currentState = _uiState.value
        val loadingState = stateManager.updateLoading(currentState, true)
        _uiState.value = loadingState

        viewModelScope.launch {
            try {
                // Load style alternatives for the carousels
                loadStyleAlternatives()
                Log.d(TAG, "REFACTORED_VM: Initial data load completed successfully")
            } catch (exception: Exception) {
                Log.e(TAG, "REFACTORED_VM: Error loading initial data", exception)

                val currentState = _uiState.value
                val errorState = stateManager.updateError(currentState, "Failed to load initial data: ${exception.message}")
                _uiState.value = errorState

                effectHandler.handleError(
                    errorMessage = "Failed to load initial data: ${exception.message}",
                    errorType = "initial_data_loading_error",
                    coroutineScope = viewModelScope
                )
            }
        }
    }

    /**
     * Loads style alternatives for the battery and emoji carousels.
     * Fetches emoji items from the same category as the current style and converts them to BatteryStyle objects.
     * This implements Phase 1 requirement for populating both carousels with component-specific data.
     */
    private suspend fun loadStyleAlternatives() {
        try {
            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Starting to load style alternatives")

            val currentState = _uiState.value
            val currentStyleId = currentState.customizationConfig.selectedStyleId

            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Current style ID: $currentStyleId")

            // For now, we'll load from a default category since we don't have category info from the current style
            // In a real implementation, you might want to determine the category from the current style
            val categoryId = "animal_category" // Default category for testing

            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Loading emoji items for category: $categoryId")

            // Load emoji items from the same category
            val emojiItems = emojiItemService.getEmojiItemsByCategory(categoryId)
            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Loaded ${emojiItems.size} emoji items")

            // Convert to BatteryStyle objects
            val batteryStyles = emojiItems.map { it.toBatteryStyle() }
            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Converted to ${batteryStyles.size} battery styles")

            // Update state with the loaded alternatives
            updateState {
                copy(
                    availableBatteryStyles = batteryStyles,
                    availableEmojiStyles = batteryStyles, // Same data source for both carousels in Phase 1
                    isLoading = false,
                    isInitialLoadComplete = true
                )
            }

            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Successfully updated state with ${batteryStyles.size} styles")
            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Battery styles count: ${batteryStyles.size}")
            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Emoji styles count: ${batteryStyles.size}")

            // Log details of loaded styles for debugging
            batteryStyles.take(3).forEachIndexed { index, style ->
                Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Style $index: name='${style.name}', id='${style.id}', batteryUrl='${style.batteryImageUrl}', emojiUrl='${style.emojiImageUrl}'")
            }

        } catch (exception: Exception) {
            Log.e(TAG, "LOAD_STYLE_ALTERNATIVES: Error loading style alternatives", exception)
            updateState {
                copy(
                    isLoading = false,
                    errorMessage = "Failed to load style alternatives: ${exception.message}"
                )
            }
        }
    }
    
    /**
     * Initializes the screen with a specific battery style - refactored to use StateManager
     */
    private fun initializeWithStyle(style: BatteryStyle) {
        Log.d(TAG, "REFACTORED_VM: Initializing with style: ${style.name}")

        val currentState = _uiState.value
        val newState = stateManager.initializeWithStyle(currentState, style)
        _uiState.value = newState

        // Load alternatives for the carousels
        viewModelScope.launch {
            loadStyleAlternatives()
        }

        Log.d(TAG, "REFACTORED_VM: Initialization complete with style: ${style.name}")
    }

    /**
     * Selects a battery component style - refactored to use StateManager and EffectHandler
     */
    private fun selectBatteryStyle(style: BatteryStyle) {
        Log.d(TAG, "REFACTORED_VM: Selecting battery component: ${style.name}")

        val currentState = _uiState.value
        // Use updateWithBatteryStyle for premium validation
        val newState = stateManager.updateWithBatteryStyle(currentState, style)
        _uiState.value = newState

        // Handle analytics and preview update through EffectHandler
        effectHandler.handleStyleSelection(style, "battery", viewModelScope)
        refreshPreview()

        Log.d(TAG, "REFACTORED_VM: Battery component updated: ${style.name}")
    }

    /**
     * Selects an emoji component style - refactored to use StateManager and EffectHandler
     */
    private fun selectEmojiStyle(style: BatteryStyle) {
        Log.d(TAG, "REFACTORED_VM: Selecting emoji component: ${style.name}")

        val currentState = _uiState.value
        // Use updateWithEmojiStyle for premium validation
        val newState = stateManager.updateWithEmojiStyle(currentState, style)
        _uiState.value = newState

        // Handle analytics and preview update through EffectHandler
        effectHandler.handleStyleSelection(style, "emoji", viewModelScope)
        refreshPreview()

        Log.d(TAG, "REFACTORED_VM: Emoji component updated: ${style.name}")
    }
    
    /**
     * Toggles the global emoji battery feature - refactored to use EffectHandler
     */
    private fun toggleGlobalFeature(isEnabled: Boolean) {
        Log.d(TAG, "REFACTORED_VM: toggleGlobalFeature called with isEnabled=$isEnabled")

        // Use EffectHandler for permission handling and state updates
        // This is similar to how BatteryGalleryViewModel handles global toggle
        viewModelScope.launch {
            if (isEnabled) {
                Log.d(TAG, "REFACTORED_VM: User wants to enable overlay feature")

                // The EffectHandler will handle permission checks and state updates
                effectHandler.handleApplyCustomization(
                    config = _uiState.value.customizationConfig,
                    coroutineScope = viewModelScope,
                    onSuccess = {
                        val currentState = _uiState.value
                        val newState = stateManager.updateGlobalEnabled(currentState, true)
                        _uiState.value = newState
                    },
                    onError = { errorMessage ->
                        val currentState = _uiState.value
                        val newState = stateManager.updateError(currentState, errorMessage)
                        _uiState.value = newState
                    },
                    onPermissionRequired = {
                        val currentState = _uiState.value
                        val newState = stateManager.updateGlobalEnabled(currentState, false)
                        _uiState.value = newState
                    }
                )
            } else {
                Log.d(TAG, "REFACTORED_VM: User wants to disable overlay feature")

                // Disable through repository and update state
                try {
                    val disabledConfig = _uiState.value.customizationConfig.copy(isGlobalEnabled = false)
                    val result = customizationRepository.saveCustomizationConfig(disabledConfig)

                    if (result.isSuccess) {
                        val currentState = _uiState.value
                        val newState = stateManager.updateGlobalEnabled(currentState, false)
                        _uiState.value = newState

                        effectHandler.handleSuccess("Emoji battery feature disabled", viewModelScope)
                    }
                } catch (exception: Exception) {
                    Log.e(TAG, "REFACTORED_VM: Error disabling feature", exception)
                    effectHandler.handleError("Failed to disable feature", "toggle_error", viewModelScope)
                }
            }
        }
    }

    /**
     * Enables the overlay feature after permissions are granted.
     */
    private fun enableOverlayFeature() {
        Log.d(TAG, "TOGGLE_DEBUG: enableOverlayFeature() called")
        viewModelScope.launch {
            try {
                Log.d(TAG, "TOGGLE_DEBUG: Calling customizationRepository.updateGlobalEnabled(true)")
                // Update configuration to enable global feature
                val result = customizationRepository.updateGlobalEnabled(true)
                Log.d(TAG, "TOGGLE_DEBUG: Repository update result: isSuccess=${result.isSuccess}")

                if (result.isSuccess) {
                    Log.d(TAG, "TOGGLE_DEBUG: Repository update successful, updating UI state to enabled")
                    updateState { copy(isGlobalEnabled = true) }
                    // Start the accessibility service
                    val serviceIntent = Intent(context, EmojiBatteryAccessibilityService::class.java)
                    serviceIntent.action = EmojiBatteryAccessibilityService.ACTION_SHOW_OVERLAY
                    context.startService(serviceIntent)
                    Log.d(TAG, "TOGGLE_DEBUG: Overlay feature enabled successfully and service started")
                } else {
                    Log.e(TAG, "TOGGLE_DEBUG: Failed to enable overlay feature", result.exceptionOrNull())
                    updateState {
                        copy(
                            isGlobalEnabled = false,
                            errorMessage = "Failed to enable emoji battery feature"
                        )
                    }
                    Log.d(TAG, "TOGGLE_DEBUG: State updated with error - isGlobalEnabled=false")
                }
            } catch (exception: Exception) {
                Log.e(TAG, "TOGGLE_DEBUG: Exception enabling overlay feature", exception)
                updateState {
                    copy(
                        isGlobalEnabled = false,
                        errorMessage = "Error enabling emoji battery feature"
                    )
                }
                Log.d(TAG, "TOGGLE_DEBUG: State updated with exception - isGlobalEnabled=false")
            }
        }
    }

    /**
     * Disables the overlay feature.
     */
    private fun disableOverlayFeature() {
        Log.d(TAG, "TOGGLE_DEBUG: disableOverlayFeature() called")
        viewModelScope.launch {
            try {
                Log.d(TAG, "TOGGLE_DEBUG: Calling customizationRepository.updateGlobalEnabled(false)")
                // Update configuration to disable global feature
                val result = customizationRepository.updateGlobalEnabled(false)
                Log.d(TAG, "TOGGLE_DEBUG: Repository update result: isSuccess=${result.isSuccess}")

                if (result.isSuccess) {
                    Log.d(TAG, "TOGGLE_DEBUG: Repository update successful, updating UI state to disabled")
                    updateState { copy(isGlobalEnabled = false) }
                    // Stop the accessibility service
                    val serviceIntent = Intent(context, EmojiBatteryAccessibilityService::class.java)
                    serviceIntent.action = EmojiBatteryAccessibilityService.ACTION_HIDE_OVERLAY
                    context.startService(serviceIntent)
                    Log.d(TAG, "TOGGLE_DEBUG: Overlay feature disabled successfully and service stopped")
                } else {
                    Log.e(TAG, "TOGGLE_DEBUG: Failed to disable overlay feature", result.exceptionOrNull())
                    updateState {
                        copy(
                            errorMessage = "Failed to disable emoji battery feature"
                        )
                    }
                    Log.d(TAG, "TOGGLE_DEBUG: State updated with error")
                }
            } catch (exception: Exception) {
                Log.e(TAG, "TOGGLE_DEBUG: Exception disabling overlay feature", exception)
                updateState {
                    copy(
                        errorMessage = "Error disabling emoji battery feature"
                    )
                }
                Log.d(TAG, "TOGGLE_DEBUG: State updated with exception")
            }
        }
    }

    /**
     * Handles the result of permission requests - refactored to use EffectHandler
     */
    fun handlePermissionResult(granted: Boolean) {
        Log.d(TAG, "REFACTORED_VM: handlePermissionResult called with granted=$granted")

        effectHandler.handlePermissionResult(
            granted = granted,
            coroutineScope = viewModelScope,
            onGranted = {
                val currentState = _uiState.value
                val newState = stateManager.updateGlobalEnabled(currentState, true)
                _uiState.value = newState
            },
            onDenied = {
                val currentState = _uiState.value
                val newState = stateManager.updateGlobalEnabled(currentState, false)
                _uiState.value = newState
            }
        )
    }

    /**
     * Updates show emoji toggle - refactored to use StateManager and EffectHandler
     */
    private fun updateShowEmoji(show: Boolean) {
        Log.d(TAG, "REFACTORED_VM: Updating show emoji: $show")

        val currentState = _uiState.value
        val newState = stateManager.updateShowEmoji(currentState, show)
        _uiState.value = newState

        effectHandler.handleCustomizationChanged("show_emoji", show.toString(), viewModelScope)
        refreshPreview()
    }

    /**
     * Updates show percentage toggle - refactored to use StateManager and EffectHandler
     */
    private fun updateShowPercentage(show: Boolean) {
        Log.d(TAG, "REFACTORED_VM: Updating show percentage: $show")

        val currentState = _uiState.value
        val newState = stateManager.updateShowPercentage(currentState, show)
        _uiState.value = newState

        effectHandler.handleCustomizationChanged("show_percentage", show.toString(), viewModelScope)
        refreshPreview()
    }

    /**
     * Updates percentage font size - refactored to use StateManager and EffectHandler
     */
    private fun updatePercentageFontSize(size: Int) {
        Log.d(TAG, "REFACTORED_VM: Updating percentage font size: $size")

        val currentState = _uiState.value
        val newState = stateManager.updatePercentageFontSize(currentState, size)
        _uiState.value = newState

        effectHandler.handleCustomizationChanged("font_size", size.toString(), viewModelScope)
        refreshPreview()
    }

    /**
     * Updates emoji size scale - refactored to use StateManager and EffectHandler
     */
    private fun updateEmojiSizeScale(scale: Float) {
        Log.d(TAG, "REFACTORED_VM: Updating emoji size scale: $scale")

        val currentState = _uiState.value
        val newState = stateManager.updateEmojiSizeScale(currentState, scale)
        _uiState.value = newState

        effectHandler.handleCustomizationChanged("emoji_scale", scale.toString(), viewModelScope)
        refreshPreview()
    }

    /**
     * Updates percentage color - refactored to use StateManager and EffectHandler
     */
    private fun updatePercentageColor(color: Int) {
        Log.d(TAG, "REFACTORED_VM: Updating percentage color: $color")

        val currentState = _uiState.value
        val newState = stateManager.updatePercentageColor(currentState, color)
        _uiState.value = newState

        effectHandler.handleCustomizationChanged("percentage_color", color.toString(), viewModelScope)
        refreshPreview()
    }

    /**
     * Shows the color picker - refactored to use StateManager and EffectHandler
     */
    private fun showColorPicker() {
        Log.d(TAG, "REFACTORED_VM: Showing color picker")

        val currentState = _uiState.value
        val newState = stateManager.updateColorPickerVisibility(currentState, true)
        _uiState.value = newState

        effectHandler.handleColorPickerVisibility(true, viewModelScope)
    }

    /**
     * Hides the color picker - refactored to use StateManager and EffectHandler
     */
    private fun hideColorPicker() {
        Log.d(TAG, "REFACTORED_VM: Hiding color picker")

        val currentState = _uiState.value
        val newState = stateManager.updateColorPickerVisibility(currentState, false)
        _uiState.value = newState

        effectHandler.handleColorPickerVisibility(false, viewModelScope)
    }

    /**
     * Selects a color from the color picker - refactored to use StateManager and EffectHandler
     */
    private fun selectColor(color: Int, index: Int) {
        Log.d(TAG, "REFACTORED_VM: Selecting color: $color at index $index")

        val currentState = _uiState.value
        val newState = stateManager.updatePercentageColor(currentState, color, index)
            .let { stateManager.updateColorPickerVisibility(it, false) }
        _uiState.value = newState

        effectHandler.handleCustomizationChanged("color_selection", color.toString(), viewModelScope)
        refreshPreview()
    }
    
    /**
     * Updates the preview battery level - refactored to use StateManager
     */
    private fun updatePreviewBatteryLevel(level: Int) {
        Log.d(TAG, "REFACTORED_VM: Updating preview battery level: $level")

        val currentState = _uiState.value
        val newState = stateManager.updatePreviewBatteryLevel(currentState, level)
        _uiState.value = newState
    }

    /**
     * Refreshes the preview configuration - refactored to use EffectHandler
     */
    private fun refreshPreview() {
        Log.d(TAG, "REFACTORED_VM: Refreshing preview")

        val currentState = _uiState.value
        effectHandler.handlePreviewUpdate(currentState.customizationConfig, viewModelScope)
    }

    /**
     * Applies the current customization - refactored to use EffectHandler
     */
    private fun applyCustomization() {
        Log.d(TAG, "REFACTORED_VM: Applying customization")

        val currentState = _uiState.value

        // Validate form first
        val validatedState = stateManager.validateForm(currentState)
        _uiState.value = validatedState

        if (!validatedState.canApplyChanges) {
            Log.w(TAG, "REFACTORED_VM: Cannot apply changes - validation failed")
            effectHandler.handleValidationError("Please complete all required fields", viewModelScope)
            return
        }

        // Set saving state
        val savingState = stateManager.updateSaving(validatedState, true)
        _uiState.value = savingState

        // Create customization config
        val batteryStyle = validatedState.selectedBatteryStyle!!
        val emojiStyle = validatedState.selectedEmojiStyle!!

        val customizationConfig = createCustomizationConfig(batteryStyle, emojiStyle, validatedState)

        // Check if premium access is required and handle accordingly
        val requiresAd = stateManager.requiresPremiumAccess(validatedState)

        if (requiresAd) {
            Log.d(TAG, "REFACTORED_VM: Premium selection detected and user doesn't have premium access, showing reward ad")
            effectHandler.handlePremiumCustomization(customizationConfig, viewModelScope)
        } else {
            if (validatedState.hasAnyPremiumSelection) {
                Log.d(TAG, "REFACTORED_VM: Premium selection detected but user has premium access, applying directly")
            } else {
                Log.d(TAG, "REFACTORED_VM: Free selection, applying directly")
            }
            effectHandler.handleApplyCustomization(
                config = customizationConfig,
                coroutineScope = viewModelScope,
                onSuccess = {
                    val currentState = _uiState.value
                    val successState = stateManager.updateSaving(currentState, false)
                    _uiState.value = successState
                },
                onError = { errorMessage ->
                    val currentState = _uiState.value
                    val errorState = stateManager.updateError(currentState, errorMessage)
                    _uiState.value = errorState
                },
                onPermissionRequired = {
                    val currentState = _uiState.value
                    val permissionState = stateManager.updateSaving(currentState, false)
                    _uiState.value = permissionState
                }
            )
        }
    }

    /**
     * Helper method to create customization config from selected styles
     */
    private fun createCustomizationConfig(
        batteryStyle: BatteryStyle,
        emojiStyle: BatteryStyle,
        state: CustomizeUiState
    ): CustomizationConfig {
        Log.d(TAG, "REFACTORED_VM: Creating customization config")
        Log.d(TAG, "REFACTORED_VM: Battery: ${batteryStyle.name}, Emoji: ${emojiStyle.name}")

        // Create composite style identifier
        val compositeStyleId = if (batteryStyle.id == emojiStyle.id) {
            batteryStyle.id
        } else {
            "${batteryStyle.id}_${emojiStyle.id}"
        }

        return CustomizationConfig.withDirectUrls(
            batteryImageUrl = batteryStyle.batteryImageUrl,
            emojiImageUrl = emojiStyle.emojiImageUrl,
            batteryStyleName = batteryStyle.name,
            emojiStyleName = emojiStyle.name,
            config = state.currentPreviewConfig
        ).copy(
            selectedStyleId = compositeStyleId,
            isGlobalEnabled = state.isGlobalEnabled
        )
    }

    /**
     * Handles premium customization application - refactored to use EffectHandler
     */
    private fun applyPremiumCustomization() {
        Log.d(TAG, "REFACTORED_VM: Starting premium customization flow")

        val currentState = _uiState.value
        val validatedState = stateManager.validateForm(currentState)
        _uiState.value = validatedState

        // Check if premium access is actually required
        val requiresAd = stateManager.requiresPremiumAccess(validatedState)

        if (!validatedState.hasAnyPremiumSelection) {
            Log.w(TAG, "REFACTORED_VM: No premium items selected, falling back to regular apply")
            applyCustomization()
            return
        }

        if (!requiresAd) {
            Log.d(TAG, "REFACTORED_VM: Premium items selected but user has premium access, applying directly")
            applyCustomization()
            return
        }

        if (!validatedState.canApplyChanges) {
            Log.w(TAG, "REFACTORED_VM: Cannot apply changes - requirements not met")
            effectHandler.handleValidationError("Please complete all required fields", viewModelScope)
            return
        }

        Log.d(TAG, "REFACTORED_VM: Premium items detected and reward ad required, using EffectHandler")

        val batteryStyle = validatedState.selectedBatteryStyle!!
        val emojiStyle = validatedState.selectedEmojiStyle!!
        val customizationConfig = createCustomizationConfig(batteryStyle, emojiStyle, validatedState)

        effectHandler.handlePremiumCustomization(customizationConfig, viewModelScope)
    }

    /**
     * Resets all customizations to defaults - refactored to use StateManager
     */
    private fun resetToDefaults() {
        Log.d(TAG, "REFACTORED_VM: Resetting to defaults")

        val currentState = _uiState.value
        val newState = stateManager.resetToDefaults(currentState)
        _uiState.value = newState

        refreshPreview()
    }

    /**
     * Navigates back to the previous screen - refactored to use EffectHandler
     */
    private fun navigateBack() {
        Log.d(TAG, "REFACTORED_VM: Navigating back")
        effectHandler.handleNavigateBack(viewModelScope)
    }
    
    /**
     * Handles resume lifecycle event - refactored
     */
    private fun onResume() {
        Log.d(TAG, "REFACTORED_VM: onResume")
        effectHandler.handleEngagement("screen_resume", System.currentTimeMillis(), viewModelScope)
    }

    /**
     * Handles pause lifecycle event - refactored
     */
    private fun onPause() {
        Log.d(TAG, "REFACTORED_VM: onPause")
        effectHandler.handleEngagement("screen_pause", System.currentTimeMillis(), viewModelScope)
    }

    /**
     * Retries loading data after an error
     */
    private fun retryLoad() {
        Log.d(TAG, "REFACTORED_VM: Retrying load")
        loadInitialData()
    }

    /**
     * Clears error state
     */
    private fun clearError() {
        Log.d(TAG, "REFACTORED_VM: Clearing error")

        val currentState = _uiState.value
        val newState = stateManager.updateError(currentState, null)
        _uiState.value = newState
    }

    /**
     * Validates the current form
     */
    private fun validateForm() {
        Log.d(TAG, "REFACTORED_VM: Validating form")

        val currentState = _uiState.value
        val validatedState = stateManager.validateForm(currentState)
        _uiState.value = validatedState

        if (!validatedState.isFormValid) {
            val errorMessage = validatedState.validationErrors.firstOrNull() ?: "Form validation failed"
            effectHandler.handleValidationError(errorMessage, viewModelScope)
        }
    }

    /**
     * Shows validation error
     */
    private fun showValidationError(message: String) {
        Log.d(TAG, "REFACTORED_VM: Showing validation error: $message")
        effectHandler.handleValidationError(message, viewModelScope)
    }

    /**
     * Helper function to update state safely using StateManager
     */
    private fun updateState(update: CustomizeUiState.() -> CustomizeUiState) {
        val oldState = _uiState.value
        val newState = oldState.update()
        _uiState.value = newState
        Log.d(TAG, "REFACTORED_VM: State updated - selectedBatteryStyle: ${newState.selectedBatteryStyle?.name}, selectedEmojiStyle: ${newState.selectedEmojiStyle?.name}")
    }

}
