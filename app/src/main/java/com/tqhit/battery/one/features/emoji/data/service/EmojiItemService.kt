package com.tqhit.battery.one.features.emoji.data.service

import com.google.gson.Gson
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.features.emoji.domain.model.EmojiItem
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service responsible for fetching and parsing emoji items from Firebase Remote Config.
 * Fetches items by category ID and provides fallback to default items.
 * 
 * This service follows the established patterns in the app:
 * - Uses Firebase Remote Config for dynamic content
 * - Provides comprehensive error handling with detailed logging
 * - Follows SOLID principles and clean architecture
 * - Uses coroutines for asynchronous operations
 */
@Singleton
class EmojiItemService @Inject constructor(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val gson: Gson
) {
    companion object {
        private const val TAG = "EmojiItemService"
    }
    
    /**
     * Fetches emoji items for a specific category from Firebase Remote Config.
     * Returns only items with status=true, sorted by priority.
     *
     * The Firebase Remote Config SDK automatically handles fallback to remote_config_defaults.xml
     * when network data is unavailable, so no custom fallback logic is needed.
     *
     * @param categoryId The category ID to fetch items for (e.g., "animal_category")
     * @return List of valid emoji items for the category, sorted by priority
     */
    suspend fun getEmojiItemsByCategory(categoryId: String): List<EmojiItem> = withContext(Dispatchers.IO) {
        try {
            BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: ========== FETCHING EMOJI ITEMS ==========")
            BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Category: $categoryId")
            BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Thread: ${Thread.currentThread().name}")
            BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Timestamp: ${System.currentTimeMillis()}")

            // Validate category ID before proceeding
            if (!isValidCategoryId(categoryId)) {
                BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG: Invalid category ID: '$categoryId'")
                return@withContext emptyList()
            }

            val jsonString = remoteConfigHelper.getString(categoryId)
            BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Retrieved JSON string length: ${jsonString.length}")

            // Enhanced JSON validation and debugging
            if (jsonString.isBlank()) {
                BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG: Empty JSON string for category: $categoryId")
                BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG: This may indicate remote config is not properly initialized")
                return@withContext emptyList()
            }

            // Log first 200 characters of JSON for debugging (without exposing full content)
            val jsonPreview = if (jsonString.length > 200) {
                jsonString.substring(0, 200) + "..."
            } else {
                jsonString
            }
            BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: JSON preview: $jsonPreview")

            val items = parseEmojiItems(jsonString, categoryId)
            val validItems = filterAndSortItems(items, categoryId)

            BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: ========== FETCH COMPLETE ==========")
            BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Total parsed items: ${items.size}")
            BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Valid items after filtering: ${validItems.size}")
            BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Success rate: ${if (items.isNotEmpty()) (validItems.size * 100 / items.size) else 0}%")

            return@withContext validItems

        } catch (e: Exception) {
            BatteryLogger.e(TAG, "DATA_SERVICE_DEBUG: ========== FETCH FAILED ==========", e)
            BatteryLogger.e(TAG, "DATA_SERVICE_DEBUG: Category: $categoryId")
            BatteryLogger.e(TAG, "DATA_SERVICE_DEBUG: Error type: ${e.javaClass.simpleName}")
            BatteryLogger.e(TAG, "DATA_SERVICE_DEBUG: Error message: ${e.message}")
            BatteryLogger.e(TAG, "DATA_SERVICE_DEBUG: Returning empty list as fallback")
            return@withContext emptyList()
        }
    }
    
    /**
     * Parses JSON string into list of EmojiItem objects.
     *
     * @param jsonString The JSON string from remote config
     * @param categoryId The category ID for logging context
     * @return List of parsed emoji items, or empty list if parsing fails
     */
    private fun parseEmojiItems(jsonString: String, categoryId: String): List<EmojiItem> {
        return try {
            BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Starting JSON parsing for category: $categoryId")

            // Validate JSON structure before parsing
            if (!jsonString.trim().startsWith("[") || !jsonString.trim().endsWith("]")) {
                BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG: JSON does not appear to be an array for category: $categoryId")
                BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG: JSON starts with: '${jsonString.take(50)}'")
                return emptyList()
            }

            val items = gson.fromJson(jsonString, Array<EmojiItem>::class.java).toList()
            BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Successfully parsed ${items.size} emoji items for category: $categoryId")

            // Enhanced validation and debugging for parsed items
            if (items.isEmpty()) {
                BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG: Parsed items list is empty for category: $categoryId")
                BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG: This may indicate the JSON array is empty or malformed")
            } else {
                // Log sample of parsed items for debugging
                val sampleSize = minOf(3, items.size)
                BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Sample of parsed items (first $sampleSize):")
                items.take(sampleSize).forEachIndexed { index, item ->
                    BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG:   [$index] ID: '${item.id}', Name: '${item.name}', Status: ${item.status}, Premium: ${item.isPremium}")
                    BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG:       Thumbnail: '${item.thumbnail}'")
                    BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG:       Photo: '${item.photo}'")
                    BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG:       Custom fields: battery='${item.customFields?.battery}', emoji='${item.customFields?.emoji}'")
                }
            }

            items
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "DATA_SERVICE_DEBUG: JSON parsing failed for category: $categoryId", e)
            BatteryLogger.e(TAG, "DATA_SERVICE_DEBUG: Exception details:")
            BatteryLogger.e(TAG, "DATA_SERVICE_DEBUG:   - Type: ${e.javaClass.simpleName}")
            BatteryLogger.e(TAG, "DATA_SERVICE_DEBUG:   - Message: ${e.message}")
            BatteryLogger.e(TAG, "DATA_SERVICE_DEBUG:   - Cause: ${e.cause?.message}")

            // Log JSON structure for debugging (first 500 chars)
            val jsonDebug = if (jsonString.length > 500) {
                jsonString.substring(0, 500) + "... (truncated)"
            } else {
                jsonString
            }
            BatteryLogger.e(TAG, "DATA_SERVICE_DEBUG: Problematic JSON: $jsonDebug")

            emptyList()
        }
    }
    
    /**
     * Filters and sorts emoji items by validity and status.
     * Only returns items with status=true and valid data, sorted by priority.
     *
     * @param items The list of items to filter and sort
     * @param categoryId The category ID for logging context
     * @return List of valid items sorted by priority
     */
    private fun filterAndSortItems(items: List<EmojiItem>, categoryId: String): List<EmojiItem> {
        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Starting filtering and sorting for category: $categoryId")
        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Input items count: ${items.size}")

        // Track filtering statistics
        var disabledCount = 0
        var invalidCount = 0
        var validCount = 0

        val validItems = items.filter { item ->
            val isItemValid = item.isValid()
            val isEnabled = item.status
            val isValid = isItemValid && isEnabled

            when {
                !isEnabled -> {
                    disabledCount++
                    BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Filtering out disabled item: '${item.id}' (${item.name}) in category: $categoryId")
                }
                !isItemValid -> {
                    invalidCount++
                    BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG: Filtering out invalid item: '${item.id}' (${item.name}) in category: $categoryId")
                    BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG:   - Validation details: ID blank=${item.id.isBlank()}, CategoryID blank=${item.categoryId.isBlank()}, Name blank=${item.name.isBlank()}, Priority=${item.priority}")
                }
                else -> {
                    validCount++
                    BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Valid item: '${item.id}' (${item.name}) - Priority: ${item.priority}, Premium: ${item.isPremium}")
                }
            }

            isValid
        }.sortedBy { it.priority }

        // Log comprehensive filtering statistics
        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Filtering complete for category: $categoryId")
        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG:   - Input items: ${items.size}")
        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG:   - Valid items: $validCount")
        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG:   - Disabled items: $disabledCount")
        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG:   - Invalid items: $invalidCount")
        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG:   - Final count: ${validItems.size}")
        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG:   - Success rate: ${if (items.isNotEmpty()) (validItems.size * 100 / items.size) else 0}%")

        // Log detailed item information for debugging
        if (validItems.isNotEmpty()) {
            BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Final valid items for category: $categoryId")
            validItems.forEachIndexed { index, item ->
                BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG:   [$index] ${item.name} (id=${item.id}, priority=${item.priority}, premium=${item.isPremium})")

                // Log image URL availability for debugging visual issues
                val imageUrls = item.getAllImageUrls()
                val hasValidImages = imageUrls.values.any { it.isNotBlank() }
                BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG:       Images available: $hasValidImages")
                if (!hasValidImages) {
                    BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG:       WARNING: No valid image URLs found for item '${item.id}'")
                }
            }
        } else {
            BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG: No valid items found for category: $categoryId")
            BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG: This will result in empty gallery for this category")
        }

        return validItems
    }
    

    
    /**
     * Validates if a category ID is supported by the service.
     *
     * @param categoryId The category ID to validate
     * @return true if the category ID is valid and supported
     */
    private fun isValidCategoryId(categoryId: String): Boolean {
        val isValid = categoryId.isNotBlank() && getAvailableCategoryIds().contains(categoryId)

        if (!isValid) {
            BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG: Invalid category ID: '$categoryId'")
            BatteryLogger.w(TAG, "DATA_SERVICE_DEBUG: Valid categories: ${getAvailableCategoryIds().joinToString(", ")}")
        }

        return isValid
    }

    /**
     * Gets all available category IDs that have emoji items in remote config.
     * This can be used to validate which categories have content available.
     *
     * @return List of category IDs that should have emoji items
     */
    fun getAvailableCategoryIds(): List<String> {
        return listOf(
            "hot_category",
            "brainrot_category",
            "character_category",
            "heart_category",
            "cute_category",
            "sticker3d_category",
            "emotion_category",
            "animal_category",
            "food_category",
            "other_category"
        )
    }

    /**
     * Validates the health of emoji data loading for all categories.
     * This method can be used for diagnostics and monitoring.
     *
     * @return Map of category ID to validation result
     */
    suspend fun validateAllCategories(): Map<String, CategoryValidationResult> = withContext(Dispatchers.IO) {
        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: ========== VALIDATING ALL CATEGORIES ==========")

        val results = mutableMapOf<String, CategoryValidationResult>()

        getAvailableCategoryIds().forEach { categoryId ->
            try {
                BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Validating category: $categoryId")

                val items = getEmojiItemsByCategory(categoryId)
                val hasItems = items.isNotEmpty()
                val itemsWithImages = items.count { item ->
                    item.getAllImageUrls().values.any { it.isNotBlank() }
                }

                val result = CategoryValidationResult(
                    categoryId = categoryId,
                    isValid = hasItems,
                    itemCount = items.size,
                    itemsWithImages = itemsWithImages,
                    errorMessage = if (!hasItems) "No valid items found" else null
                )

                results[categoryId] = result

                BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Category '$categoryId' validation: ${if (hasItems) "PASS" else "FAIL"} (${items.size} items, $itemsWithImages with images)")

            } catch (e: Exception) {
                val result = CategoryValidationResult(
                    categoryId = categoryId,
                    isValid = false,
                    itemCount = 0,
                    itemsWithImages = 0,
                    errorMessage = e.message
                )

                results[categoryId] = result
                BatteryLogger.e(TAG, "DATA_SERVICE_DEBUG: Category '$categoryId' validation failed", e)
            }
        }

        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: ========== VALIDATION COMPLETE ==========")
        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Total categories: ${results.size}")
        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Valid categories: ${results.values.count { it.isValid }}")
        BatteryLogger.d(TAG, "DATA_SERVICE_DEBUG: Invalid categories: ${results.values.count { !it.isValid }}")

        results
    }

    /**
     * Data class representing the validation result for a category.
     */
    data class CategoryValidationResult(
        val categoryId: String,
        val isValid: Boolean,
        val itemCount: Int,
        val itemsWithImages: Int,
        val errorMessage: String?
    )
    
    /**
     * Checks if a category ID is supported by the remote config.
     * 
     * @param categoryId The category ID to check
     * @return true if the category is supported, false otherwise
     */
    fun isCategorySupportedInRemoteConfig(categoryId: String): Boolean {
        return getAvailableCategoryIds().contains(categoryId)
    }
}
